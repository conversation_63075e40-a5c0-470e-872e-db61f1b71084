"use client";

import React, { useState } from "react";
import { EyeIcon, EyeSlashIcon, ArrowLeftIcon } from "@heroicons/react/24/outline";
import { useColor } from "@/context/ColorContext";
import { Form, Input, message, Skeleton } from "antd";
import { Mail, Phone } from "lucide-react";
import { useRouter } from "next/navigation";

interface ForgotPasswordProps {
  onBack: () => void;
}

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ onBack }) => {
  const { theme } = useColor();
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [step, setStep] = useState<"email" | "code" | "newPassword">("email");
  const [verificationCode, setVerificationCode] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const onSubmitEmail = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setStep("code");
      message.success("Mã xác thực đã được gửi đến email của bạn!");
    }, 1500);
  };

  const onSubmitCode = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setStep("newPassword");
      message.success("Mã xác thực hợp lệ!");
    }, 1000);
  };

  const onSubmitNewPassword = () => {
    if (newPassword !== confirmPassword) {
      message.error("Mật khẩu xác nhận không khớp!");
      return;
    }
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      message.success("Mật khẩu đã được đặt lại thành công!");
      onBack(); // Go back to login
    }, 1500);
  };

  const renderEmailStep = () => (
    <div className="animate-fadeIn">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-2 animate-slideDown">Quên mật khẩu</h2>
        <p className="text-gray-600 text-sm animate-slideDown animation-delay-100">
          Nhập email của bạn để nhận mã xác thực
        </p>
      </div>

      <Form
        layout="vertical"
        className="space-y-4"
        onFinish={onSubmitEmail}
        form={form}
      >
        <Form.Item
          name="email"
          rules={[
            { required: true, message: "Vui lòng nhập email" },
            { type: "email", message: "Email không hợp lệ" },
          ]}
        >
          <Input
            placeholder="Nhập email của bạn"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="h-12 px-4 border border-gray-300 rounded-md"
            prefix={<Mail className="w-4 h-4 text-gray-400" />}
          />
        </Form.Item>

        <button
          type="submit"
          disabled={loading}
          className={`w-full h-12 ${theme.primary} hover:bg-blue-900 ${theme.text} font-medium rounded-md flex items-center justify-center gap-2 transition-all duration-300 disabled:opacity-50 transform hover:scale-105 animate-slideUp animation-delay-200`}
        >
          {loading ? "Đang gửi..." : "Gửi mã xác thực"}
        </button>
      </Form>
    </div>
  );

  const renderCodeStep = () => (
    <div className="animate-fadeIn">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-2 animate-slideDown">Xác thực email</h2>
        <p className="text-gray-600 text-sm animate-slideDown animation-delay-100">
          Nhập mã xác thực đã được gửi đến email: <strong className="text-blue-600">{email}</strong>
        </p>
      </div>

      <Form
        layout="vertical"
        className="space-y-4"
        onFinish={onSubmitCode}
        form={form}
      >
        <Form.Item
          name="code"
          rules={[
            { required: true, message: "Vui lòng nhập mã xác thực" },
            { len: 6, message: "Mã xác thực phải có 6 ký tự" },
          ]}
        >
          <Input
            placeholder="Nhập mã xác thực 6 số"
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value)}
            className="h-12 px-4 border border-gray-300 rounded-md text-center text-lg tracking-widest"
            maxLength={6}
          />
        </Form.Item>

        <button
          type="submit"
          disabled={loading}
          className={`w-full h-12 ${theme.primary} hover:bg-blue-900 ${theme.text} font-medium rounded-md flex items-center justify-center gap-2 transition-all duration-300 disabled:opacity-50 transform hover:scale-105 animate-slideUp animation-delay-200`}
        >
          {loading ? "Đang xác thực..." : "Xác thực"}
        </button>

        <div className="text-center animate-slideUp animation-delay-300">
          <button
            type="button"
            className="text-blue-600 hover:underline text-sm transition-colors duration-200 hover:text-blue-800"
            onClick={() => setStep("email")}
          >
            Gửi lại mã xác thực
          </button>
        </div>
      </Form>
    </div>
  );

  const renderNewPasswordStep = () => (
    <>
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Đặt lại mật khẩu</h2>
        <p className="text-gray-600 text-sm">
          Nhập mật khẩu mới cho tài khoản của bạn
        </p>
      </div>

      <Form
        layout="vertical"
        className="space-y-4"
        onFinish={onSubmitNewPassword}
        form={form}
      >
        <Form.Item
          name="newPassword"
          rules={[
            { required: true, message: "Vui lòng nhập mật khẩu mới" },
            { min: 6, message: "Mật khẩu phải có ít nhất 6 ký tự" },
          ]}
        >
          <Input.Password
            placeholder="Mật khẩu mới"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            className="h-12 px-4 border border-gray-300 rounded-md"
            iconRender={(visible) =>
              visible ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />
            }
          />
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          rules={[
            { required: true, message: "Vui lòng xác nhận mật khẩu" },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('Mật khẩu xác nhận không khớp!'));
              },
            }),
          ]}
        >
          <Input.Password
            placeholder="Xác nhận mật khẩu mới"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            className="h-12 px-4 border border-gray-300 rounded-md"
            iconRender={(visible) =>
              visible ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />
            }
          />
        </Form.Item>

        <button
          type="submit"
          disabled={loading}
          className={`w-full h-12 ${theme.primary} hover:bg-blue-900 ${theme.text} font-medium rounded-md flex items-center justify-center gap-2 transition-colors disabled:opacity-50`}
        >
          {loading ? "Đang cập nhật..." : "Đặt lại mật khẩu"}
        </button>
      </Form>
    </>
  );

  return (
    <div className="w-full h-screen relative flex items-center justify-center">
      {/* Background image using img tag */}
      <img
        src="/login-bg.jpg"
        alt="login background"
        className="absolute inset-0 w-full h-full object-cover"
        style={{ zIndex: -1 }}
      />
      
      {/* Overlay */}
      <div className="absolute inset-0 bg-opacity-20" style={{ zIndex: 0 }}></div>

      {/* Forgot Password Card */}
      <div className="relative z-10 w-full max-w-md mx-4">
        {loading && step === "email" ? (
          <div className="flex h-screen items-center justify-center">
            <Skeleton active round avatar title />
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-lg p-8">
            {/* Logo and Brand */}
            <div className="flex flex-col items-center mb-8">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                </div>
                <span className="text-2xl font-bold text-gray-800">MiniMart</span>
              </div>
            </div>

            {/* Back button */}
            <button
              onClick={onBack}
              className="w-full h-10 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md flex items-center justify-center gap-2 transition-all duration-300 transform hover:scale-105 mb-6"
            >
              <ArrowLeftIcon className="w-4 h-4" />
              <span className="text-sm">Quay lại đăng nhập</span>
            </button>

            {/* Render current step */}
            {step === "email" && renderEmailStep()}
            {step === "code" && renderCodeStep()}
            {step === "newPassword" && renderNewPasswordStep()}

            {/* Footer */}
            <div className="mt-8 text-center text-sm text-gray-500">
              <div className="flex items-center justify-center gap-4">
                <span className="flex items-center gap-1">
                  <Phone className="w-4 h-4" />
                  Hỗ trợ: 1900 6522
                </span>
                <span className="flex items-center gap-1">
                  <img
                    src="/lg-vn.png"
                    alt="Lá cờ Việt Nam"
                    className="w-5 h-3"
                  />
                  Tiếng Việt
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ForgotPassword;
