type Theme = {
  name: string;
  primary: string;
  secondary: string;
  text: string;
};

export const themes: Theme[] = [
  {
    name: "blue",
    primary: "bg-blue-800",
    secondary: "bg-blue-200",
    text: "text-white",
  },
  {
    name: "slate",
    primary: "bg-slate-800",
    secondary: "bg-slate-300",
    text: "text-white",
  },
  {
    name: "green",
    primary: "bg-green-800",
    secondary: "bg-green-200",
    text: "text-white",
  },
  {
    name: "rose",
    primary: "bg-rose-600",
    secondary: "bg-rose-200",
    text: "text-white",
  },
  {
    name: "teal",
    primary: "bg-teal-700",
    secondary: "bg-teal-200",
    text: "text-white",
  },
  {
    name: "orange",
    primary: "bg-orange-500",
    secondary: "bg-orange-200",
    text: "text-white",
  },
  {
    name: "pink",
    primary: "bg-pink-700",
    secondary: "bg-pink-200",
    text: "text-white",
  },
];
