"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-textarea";
exports.ids = ["vendor-chunks/rc-textarea"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-textarea/es/ResizableTextArea.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _calculateNodeHeight__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./calculateNodeHeight */ \"(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js\");\n\n\n\n\n\n\nvar _excluded = [\"prefixCls\", \"defaultValue\", \"value\", \"autoSize\", \"onResize\", \"className\", \"style\", \"disabled\", \"onChange\", \"onInternalAutoSize\"];\n\n\n\n\n\n\n\nvar RESIZE_START = 0;\nvar RESIZE_MEASURING = 1;\nvar RESIZE_STABLE = 2;\nvar ResizableTextArea = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.forwardRef(function (props, ref) {\n  var _ref = props,\n    prefixCls = _ref.prefixCls,\n    defaultValue = _ref.defaultValue,\n    value = _ref.value,\n    autoSize = _ref.autoSize,\n    onResize = _ref.onResize,\n    className = _ref.className,\n    style = _ref.style,\n    disabled = _ref.disabled,\n    onChange = _ref.onChange,\n    onInternalAutoSize = _ref.onInternalAutoSize,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_ref, _excluded);\n\n  // =============================== Value ================================\n  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(defaultValue, {\n      value: value,\n      postState: function postState(val) {\n        return val !== null && val !== void 0 ? val : '';\n      }\n    }),\n    _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setMergedValue = _useMergedState2[1];\n  var onInternalChange = function onInternalChange(event) {\n    setMergedValue(event.target.value);\n    onChange === null || onChange === void 0 || onChange(event);\n  };\n\n  // ================================ Ref =================================\n  var textareaRef = react__WEBPACK_IMPORTED_MODULE_11__.useRef();\n  react__WEBPACK_IMPORTED_MODULE_11__.useImperativeHandle(ref, function () {\n    return {\n      textArea: textareaRef.current\n    };\n  });\n\n  // ============================== AutoSize ==============================\n  var _React$useMemo = react__WEBPACK_IMPORTED_MODULE_11__.useMemo(function () {\n      if (autoSize && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(autoSize) === 'object') {\n        return [autoSize.minRows, autoSize.maxRows];\n      }\n      return [];\n    }, [autoSize]),\n    _React$useMemo2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useMemo, 2),\n    minRows = _React$useMemo2[0],\n    maxRows = _React$useMemo2[1];\n  var needAutoSize = !!autoSize;\n\n  // =============================== Scroll ===============================\n  // https://github.com/ant-design/ant-design/issues/21870\n  var fixFirefoxAutoScroll = function fixFirefoxAutoScroll() {\n    try {\n      // FF has bug with jump of scroll to top. We force back here.\n      if (document.activeElement === textareaRef.current) {\n        var _textareaRef$current = textareaRef.current,\n          selectionStart = _textareaRef$current.selectionStart,\n          selectionEnd = _textareaRef$current.selectionEnd,\n          scrollTop = _textareaRef$current.scrollTop;\n\n        // Fix Safari bug which not rollback when break line\n        // This makes Chinese IME can't input. Do not fix this\n        // const { value: tmpValue } = textareaRef.current;\n        // textareaRef.current.value = '';\n        // textareaRef.current.value = tmpValue;\n\n        textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n        textareaRef.current.scrollTop = scrollTop;\n      }\n    } catch (e) {\n      // Fix error in Chrome:\n      // Failed to read the 'selectionStart' property from 'HTMLInputElement'\n      // http://stackoverflow.com/q/21177489/3040605\n    }\n  };\n\n  // =============================== Resize ===============================\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_11__.useState(RESIZE_STABLE),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState, 2),\n    resizeState = _React$useState2[0],\n    setResizeState = _React$useState2[1];\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_11__.useState(),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState3, 2),\n    autoSizeStyle = _React$useState4[0],\n    setAutoSizeStyle = _React$useState4[1];\n  var startResize = function startResize() {\n    setResizeState(RESIZE_START);\n    if (false) {}\n  };\n\n  // Change to trigger resize measure\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function () {\n    if (needAutoSize) {\n      startResize();\n    }\n  }, [value, minRows, maxRows, needAutoSize]);\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function () {\n    if (resizeState === RESIZE_START) {\n      setResizeState(RESIZE_MEASURING);\n    } else if (resizeState === RESIZE_MEASURING) {\n      var textareaStyles = (0,_calculateNodeHeight__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(textareaRef.current, false, minRows, maxRows);\n\n      // Safari has bug that text will keep break line on text cut when it's prev is break line.\n      // ZombieJ: This not often happen. So we just skip it.\n      // const { selectionStart, selectionEnd, scrollTop } = textareaRef.current;\n      // const { value: tmpValue } = textareaRef.current;\n      // textareaRef.current.value = '';\n      // textareaRef.current.value = tmpValue;\n\n      // if (document.activeElement === textareaRef.current) {\n      //   textareaRef.current.scrollTop = scrollTop;\n      //   textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n      // }\n\n      setResizeState(RESIZE_STABLE);\n      setAutoSizeStyle(textareaStyles);\n    } else {\n      fixFirefoxAutoScroll();\n    }\n  }, [resizeState]);\n\n  // We lock resize trigger by raf to avoid Safari warning\n  var resizeRafRef = react__WEBPACK_IMPORTED_MODULE_11__.useRef();\n  var cleanRaf = function cleanRaf() {\n    rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__[\"default\"].cancel(resizeRafRef.current);\n  };\n  var onInternalResize = function onInternalResize(size) {\n    if (resizeState === RESIZE_STABLE) {\n      onResize === null || onResize === void 0 || onResize(size);\n      if (autoSize) {\n        cleanRaf();\n        resizeRafRef.current = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function () {\n          startResize();\n        });\n      }\n    }\n  };\n  react__WEBPACK_IMPORTED_MODULE_11__.useEffect(function () {\n    return cleanRaf;\n  }, []);\n\n  // =============================== Render ===============================\n  var mergedAutoSizeStyle = needAutoSize ? autoSizeStyle : null;\n  var mergedStyle = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, style), mergedAutoSizeStyle);\n  if (resizeState === RESIZE_START || resizeState === RESIZE_MEASURING) {\n    mergedStyle.overflowY = 'hidden';\n    mergedStyle.overflowX = 'hidden';\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n    onResize: onInternalResize,\n    disabled: !(autoSize || onResize)\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restProps, {\n    ref: textareaRef,\n    style: mergedStyle,\n    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(prefixCls, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-disabled\"), disabled)),\n    disabled: disabled,\n    value: mergedValue,\n    onChange: onInternalChange\n  })));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResizableTextArea);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/TextArea.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-textarea/es/TextArea.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-input */ \"(ssr)/./node_modules/rc-input/es/index.js\");\n/* harmony import */ var rc_input_es_hooks_useCount__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-input/es/hooks/useCount */ \"(ssr)/./node_modules/rc-input/es/hooks/useCount.js\");\n/* harmony import */ var rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-input/es/utils/commonUtils */ \"(ssr)/./node_modules/rc-input/es/utils/commonUtils.js\");\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _ResizableTextArea__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ResizableTextArea */ \"(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\");\n\n\n\n\n\n\nvar _excluded = [\"defaultValue\", \"value\", \"onFocus\", \"onBlur\", \"onChange\", \"allowClear\", \"maxLength\", \"onCompositionStart\", \"onCompositionEnd\", \"suffix\", \"prefixCls\", \"showCount\", \"count\", \"className\", \"style\", \"disabled\", \"hidden\", \"classNames\", \"styles\", \"onResize\", \"onClear\", \"onPressEnter\", \"readOnly\", \"autoSize\", \"onKeyDown\"];\n\n\n\n\n\n\n\nvar TextArea = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().forwardRef(function (_ref, ref) {\n  var _countConfig$max;\n  var defaultValue = _ref.defaultValue,\n    customValue = _ref.value,\n    onFocus = _ref.onFocus,\n    onBlur = _ref.onBlur,\n    onChange = _ref.onChange,\n    allowClear = _ref.allowClear,\n    maxLength = _ref.maxLength,\n    onCompositionStart = _ref.onCompositionStart,\n    onCompositionEnd = _ref.onCompositionEnd,\n    suffix = _ref.suffix,\n    _ref$prefixCls = _ref.prefixCls,\n    prefixCls = _ref$prefixCls === void 0 ? 'rc-textarea' : _ref$prefixCls,\n    showCount = _ref.showCount,\n    count = _ref.count,\n    className = _ref.className,\n    style = _ref.style,\n    disabled = _ref.disabled,\n    hidden = _ref.hidden,\n    classNames = _ref.classNames,\n    styles = _ref.styles,\n    onResize = _ref.onResize,\n    onClear = _ref.onClear,\n    onPressEnter = _ref.onPressEnter,\n    readOnly = _ref.readOnly,\n    autoSize = _ref.autoSize,\n    onKeyDown = _ref.onKeyDown,\n    rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_ref, _excluded);\n  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(defaultValue, {\n      value: customValue,\n      defaultValue: defaultValue\n    }),\n    _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var formatValue = value === undefined || value === null ? '' : String(value);\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_11___default().useState(false),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState, 2),\n    focused = _React$useState2[0],\n    setFocused = _React$useState2[1];\n  var compositionRef = react__WEBPACK_IMPORTED_MODULE_11___default().useRef(false);\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_11___default().useState(null),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState3, 2),\n    textareaResized = _React$useState4[0],\n    setTextareaResized = _React$useState4[1];\n\n  // =============================== Ref ================================\n  var holderRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n  var resizableTextAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n  var getTextArea = function getTextArea() {\n    var _resizableTextAreaRef;\n    return (_resizableTextAreaRef = resizableTextAreaRef.current) === null || _resizableTextAreaRef === void 0 ? void 0 : _resizableTextAreaRef.textArea;\n  };\n  var focus = function focus() {\n    getTextArea().focus();\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_11__.useImperativeHandle)(ref, function () {\n    var _holderRef$current;\n    return {\n      resizableTextArea: resizableTextAreaRef.current,\n      focus: focus,\n      blur: function blur() {\n        getTextArea().blur();\n      },\n      nativeElement: ((_holderRef$current = holderRef.current) === null || _holderRef$current === void 0 ? void 0 : _holderRef$current.nativeElement) || getTextArea()\n    };\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(function () {\n    setFocused(function (prev) {\n      return !disabled && prev;\n    });\n  }, [disabled]);\n\n  // =========================== Select Range ===========================\n  var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_11___default().useState(null),\n    _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState5, 2),\n    selection = _React$useState6[0],\n    setSelection = _React$useState6[1];\n  react__WEBPACK_IMPORTED_MODULE_11___default().useEffect(function () {\n    if (selection) {\n      var _getTextArea;\n      (_getTextArea = getTextArea()).setSelectionRange.apply(_getTextArea, (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(selection));\n    }\n  }, [selection]);\n\n  // ============================== Count ===============================\n  var countConfig = (0,rc_input_es_hooks_useCount__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(count, showCount);\n  var mergedMax = (_countConfig$max = countConfig.max) !== null && _countConfig$max !== void 0 ? _countConfig$max : maxLength;\n\n  // Max length value\n  var hasMaxLength = Number(mergedMax) > 0;\n  var valueLength = countConfig.strategy(formatValue);\n  var isOutOfRange = !!mergedMax && valueLength > mergedMax;\n\n  // ============================== Change ==============================\n  var triggerChange = function triggerChange(e, currentValue) {\n    var cutValue = currentValue;\n    if (!compositionRef.current && countConfig.exceedFormatter && countConfig.max && countConfig.strategy(currentValue) > countConfig.max) {\n      cutValue = countConfig.exceedFormatter(currentValue, {\n        max: countConfig.max\n      });\n      if (currentValue !== cutValue) {\n        setSelection([getTextArea().selectionStart || 0, getTextArea().selectionEnd || 0]);\n      }\n    }\n    setValue(cutValue);\n    (0,rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__.resolveOnChange)(e.currentTarget, e, onChange, cutValue);\n  };\n\n  // =========================== Value Update ===========================\n  var onInternalCompositionStart = function onInternalCompositionStart(e) {\n    compositionRef.current = true;\n    onCompositionStart === null || onCompositionStart === void 0 || onCompositionStart(e);\n  };\n  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {\n    compositionRef.current = false;\n    triggerChange(e, e.currentTarget.value);\n    onCompositionEnd === null || onCompositionEnd === void 0 || onCompositionEnd(e);\n  };\n  var onInternalChange = function onInternalChange(e) {\n    triggerChange(e, e.target.value);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    if (e.key === 'Enter' && onPressEnter) {\n      onPressEnter(e);\n    }\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(e);\n  };\n  var handleFocus = function handleFocus(e) {\n    setFocused(true);\n    onFocus === null || onFocus === void 0 || onFocus(e);\n  };\n  var handleBlur = function handleBlur(e) {\n    setFocused(false);\n    onBlur === null || onBlur === void 0 || onBlur(e);\n  };\n\n  // ============================== Reset ===============================\n  var handleReset = function handleReset(e) {\n    setValue('');\n    focus();\n    (0,rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__.resolveOnChange)(getTextArea(), e, onChange);\n  };\n  var suffixNode = suffix;\n  var dataCount;\n  if (countConfig.show) {\n    if (countConfig.showFormatter) {\n      dataCount = countConfig.showFormatter({\n        value: formatValue,\n        count: valueLength,\n        maxLength: mergedMax\n      });\n    } else {\n      dataCount = \"\".concat(valueLength).concat(hasMaxLength ? \" / \".concat(mergedMax) : '');\n    }\n    suffixNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement((react__WEBPACK_IMPORTED_MODULE_11___default().Fragment), null, suffixNode, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(\"span\", {\n      className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"\".concat(prefixCls, \"-data-count\"), classNames === null || classNames === void 0 ? void 0 : classNames.count),\n      style: styles === null || styles === void 0 ? void 0 : styles.count\n    }, dataCount));\n  }\n  var handleResize = function handleResize(size) {\n    var _getTextArea2;\n    onResize === null || onResize === void 0 || onResize(size);\n    if ((_getTextArea2 = getTextArea()) !== null && _getTextArea2 !== void 0 && _getTextArea2.style.height) {\n      setTextareaResized(true);\n    }\n  };\n  var isPureTextArea = !autoSize && !showCount && !allowClear;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(rc_input__WEBPACK_IMPORTED_MODULE_7__.BaseInput, {\n    ref: holderRef,\n    value: formatValue,\n    allowClear: allowClear,\n    handleReset: handleReset,\n    suffix: suffixNode,\n    prefixCls: prefixCls,\n    classNames: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, classNames), {}, {\n      affixWrapper: classnames__WEBPACK_IMPORTED_MODULE_6___default()(classNames === null || classNames === void 0 ? void 0 : classNames.affixWrapper, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-show-count\"), showCount), \"\".concat(prefixCls, \"-textarea-allow-clear\"), allowClear))\n    }),\n    disabled: disabled,\n    focused: focused,\n    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(className, isOutOfRange && \"\".concat(prefixCls, \"-out-of-range\")),\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, style), textareaResized && !isPureTextArea ? {\n      height: 'auto'\n    } : {}),\n    dataAttrs: {\n      affixWrapper: {\n        'data-count': typeof dataCount === 'string' ? dataCount : undefined\n      }\n    },\n    hidden: hidden,\n    readOnly: readOnly,\n    onClear: onClear\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(_ResizableTextArea__WEBPACK_IMPORTED_MODULE_12__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, rest, {\n    autoSize: autoSize,\n    maxLength: maxLength,\n    onKeyDown: handleKeyDown,\n    onChange: onInternalChange,\n    onFocus: handleFocus,\n    onBlur: handleBlur,\n    onCompositionStart: onInternalCompositionStart,\n    onCompositionEnd: onInternalCompositionEnd,\n    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(classNames === null || classNames === void 0 ? void 0 : classNames.textarea),\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, styles === null || styles === void 0 ? void 0 : styles.textarea), {}, {\n      resize: style === null || style === void 0 ? void 0 : style.resize\n    }),\n    disabled: disabled,\n    prefixCls: prefixCls,\n    onResize: handleResize,\n    ref: resizableTextAreaRef,\n    readOnly: readOnly\n  })));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TextArea);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/TextArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-textarea/es/calculateNodeHeight.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateNodeStyling: () => (/* binding */ calculateNodeStyling),\n/* harmony export */   \"default\": () => (/* binding */ calculateAutoSizeStyle)\n/* harmony export */ });\n// Thanks to https://github.com/andreypopp/react-textarea-autosize/\n\n/**\n * calculateNodeHeight(uiTextNode, useCache = false)\n */\n\nvar HIDDEN_TEXTAREA_STYLE = \"\\n  min-height:0 !important;\\n  max-height:none !important;\\n  height:0 !important;\\n  visibility:hidden !important;\\n  overflow:hidden !important;\\n  position:absolute !important;\\n  z-index:-1000 !important;\\n  top:0 !important;\\n  right:0 !important;\\n  pointer-events: none !important;\\n\";\nvar SIZING_STYLE = ['letter-spacing', 'line-height', 'padding-top', 'padding-bottom', 'font-family', 'font-weight', 'font-size', 'font-variant', 'text-rendering', 'text-transform', 'width', 'text-indent', 'padding-left', 'padding-right', 'border-width', 'box-sizing', 'word-break', 'white-space'];\nvar computedStyleCache = {};\nvar hiddenTextarea;\nfunction calculateNodeStyling(node) {\n  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var nodeRef = node.getAttribute('id') || node.getAttribute('data-reactid') || node.getAttribute('name');\n  if (useCache && computedStyleCache[nodeRef]) {\n    return computedStyleCache[nodeRef];\n  }\n  var style = window.getComputedStyle(node);\n  var boxSizing = style.getPropertyValue('box-sizing') || style.getPropertyValue('-moz-box-sizing') || style.getPropertyValue('-webkit-box-sizing');\n  var paddingSize = parseFloat(style.getPropertyValue('padding-bottom')) + parseFloat(style.getPropertyValue('padding-top'));\n  var borderSize = parseFloat(style.getPropertyValue('border-bottom-width')) + parseFloat(style.getPropertyValue('border-top-width'));\n  var sizingStyle = SIZING_STYLE.map(function (name) {\n    return \"\".concat(name, \":\").concat(style.getPropertyValue(name));\n  }).join(';');\n  var nodeInfo = {\n    sizingStyle: sizingStyle,\n    paddingSize: paddingSize,\n    borderSize: borderSize,\n    boxSizing: boxSizing\n  };\n  if (useCache && nodeRef) {\n    computedStyleCache[nodeRef] = nodeInfo;\n  }\n  return nodeInfo;\n}\nfunction calculateAutoSizeStyle(uiTextNode) {\n  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var minRows = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var maxRows = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement('textarea');\n    hiddenTextarea.setAttribute('tab-index', '-1');\n    hiddenTextarea.setAttribute('aria-hidden', 'true');\n    // fix: A form field element should have an id or name attribute\n    // A form field element has neither an id nor a name attribute. This might prevent the browser from correctly autofilling the form.\n    // https://developer.mozilla.org/en-US/docs/Web/HTML/Element/textarea\n    hiddenTextarea.setAttribute('name', 'hiddenTextarea');\n    document.body.appendChild(hiddenTextarea);\n  }\n\n  // Fix wrap=\"off\" issue\n  // https://github.com/ant-design/ant-design/issues/6577\n  if (uiTextNode.getAttribute('wrap')) {\n    hiddenTextarea.setAttribute('wrap', uiTextNode.getAttribute('wrap'));\n  } else {\n    hiddenTextarea.removeAttribute('wrap');\n  }\n\n  // Copy all CSS properties that have an impact on the height of the content in\n  // the textbox\n  var _calculateNodeStyling = calculateNodeStyling(uiTextNode, useCache),\n    paddingSize = _calculateNodeStyling.paddingSize,\n    borderSize = _calculateNodeStyling.borderSize,\n    boxSizing = _calculateNodeStyling.boxSizing,\n    sizingStyle = _calculateNodeStyling.sizingStyle;\n\n  // Need to have the overflow attribute to hide the scrollbar otherwise\n  // text-lines will not calculated properly as the shadow will technically be\n  // narrower for content\n  hiddenTextarea.setAttribute('style', \"\".concat(sizingStyle, \";\").concat(HIDDEN_TEXTAREA_STYLE));\n  hiddenTextarea.value = uiTextNode.value || uiTextNode.placeholder || '';\n  var minHeight = undefined;\n  var maxHeight = undefined;\n  var overflowY;\n  var height = hiddenTextarea.scrollHeight;\n  if (boxSizing === 'border-box') {\n    // border-box: add border, since height = content + padding + border\n    height += borderSize;\n  } else if (boxSizing === 'content-box') {\n    // remove padding, since height = content\n    height -= paddingSize;\n  }\n  if (minRows !== null || maxRows !== null) {\n    // measure height of a textarea with a single row\n    hiddenTextarea.value = ' ';\n    var singleRowHeight = hiddenTextarea.scrollHeight - paddingSize;\n    if (minRows !== null) {\n      minHeight = singleRowHeight * minRows;\n      if (boxSizing === 'border-box') {\n        minHeight = minHeight + paddingSize + borderSize;\n      }\n      height = Math.max(minHeight, height);\n    }\n    if (maxRows !== null) {\n      maxHeight = singleRowHeight * maxRows;\n      if (boxSizing === 'border-box') {\n        maxHeight = maxHeight + paddingSize + borderSize;\n      }\n      overflowY = height > maxHeight ? '' : 'hidden';\n      height = Math.min(maxHeight, height);\n    }\n  }\n  var style = {\n    height: height,\n    overflowY: overflowY,\n    resize: 'none'\n  };\n  if (minHeight) {\n    style.minHeight = minHeight;\n  }\n  if (maxHeight) {\n    style.maxHeight = maxHeight;\n  }\n  return style;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-textarea/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResizableTextArea: () => (/* reexport safe */ _ResizableTextArea__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _TextArea__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TextArea */ \"(ssr)/./node_modules/rc-textarea/es/TextArea.js\");\n/* harmony import */ var _ResizableTextArea__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ResizableTextArea */ \"(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_TextArea__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGV4dGFyZWEvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrQztBQUNpQztBQUNuRSxpRUFBZSxpREFBUSIsInNvdXJjZXMiOlsiRDpcXENvZGVcXFdEUFxcRnJvbnQtRW5kXFxub2RlX21vZHVsZXNcXHJjLXRleHRhcmVhXFxlc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFRleHRBcmVhIGZyb20gXCIuL1RleHRBcmVhXCI7XG5leHBvcnQgeyBkZWZhdWx0IGFzIFJlc2l6YWJsZVRleHRBcmVhIH0gZnJvbSBcIi4vUmVzaXphYmxlVGV4dEFyZWFcIjtcbmV4cG9ydCBkZWZhdWx0IFRleHRBcmVhOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/index.js\n");

/***/ })

};
;