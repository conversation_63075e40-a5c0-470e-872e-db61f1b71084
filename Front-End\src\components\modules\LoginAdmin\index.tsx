"use client";

import React, { useState } from "react";
import { EyeI<PERSON>, EyeSlashIcon } from "@heroicons/react/24/outline";
import {
  ChartBarIcon,
  ShoppingCartIcon,
  ArrowRightOnRectangleIcon,
} from "@heroicons/react/24/solid";
import { useColor } from "@/context/ColorContext";
import { useAuth } from "@/context/AuthContext";
import { Form, Input, message, Skeleton } from "antd";
import { Lock, Mail, Phone } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";

const LoginAdmin = () => {
  const { theme } = useColor();
  const { login } = useAuth();
  const router = useRouter();

  const [account, setAccount] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [remember, setRemember] = useState(true);
  const [isForgotOpen, setIsForgotOpen] = useState(false);

  const [form] = Form.useForm();
  const [loading] = useState(false);

  const onSubmit = () => {
    console.log("Form submitted");
    // Handle login logic here
  };

  return (
    <div className="w-full h-screen relative flex items-center justify-center">
      {/* Background image */}
      <img
        src="/login-bg.jpg"
        className="absolute inset-0 w-full h-full object-cover"
        alt="login background"
      />

      {/* Overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-20"></div>

      {/* Login Card */}
      <div className="relative z-10 w-full max-w-md mx-4">
        {loading ? (
          <div className="flex h-screen items-center justify-center">
            <Skeleton active round avatar title />
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-lg p-8">
            {/* Logo and Brand */}
            <div className="flex flex-col items-center mb-8">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                </div>
                <span className="text-2xl font-bold text-gray-800">KiotViet</span>
              </div>
            </div>

            {/* Form */}
            <Form
              layout="vertical"
              className="space-y-4"
              onFinish={onSubmit}
              form={form}
            >
              {/* Username Input */}
              <Form.Item
                name="account"
                rules={[
                  { required: true, message: "Vui lòng nhập tên đăng nhập" },
                ]}
              >
                <Input
                  placeholder="Tên đăng nhập"
                  value={account}
                  onChange={(e) => setAccount(e.target.value)}
                  className="h-12 px-4 border border-gray-300 rounded-md"
                />
              </Form.Item>

              {/* Password Input */}
              <Form.Item
                name="password"
                rules={[{ required: true, message: "Vui lòng nhập mật khẩu" }]}
              >
                <div className="relative">
                  <Input.Password
                    placeholder="Mật khẩu"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="h-12 px-4 border border-gray-300 rounded-md"
                    iconRender={(visible) =>
                      visible ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />
                    }
                  />
                </div>
              </Form.Item>

              {/* Remember me and Forgot password */}
              <div className="flex items-center justify-between text-sm mb-6">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={remember}
                    onChange={(e) => setRemember(e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-gray-600">Duy trì đăng nhập</span>
                </label>
                <a
                  href="#"
                  className="text-blue-600 hover:underline"
                  onClick={(e) => {
                    e.preventDefault();
                    setIsForgotOpen(true);
                  }}
                >
                  Quên mật khẩu?
                </a>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <button
                  type="button"
                  className="flex-1 h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md flex items-center justify-center gap-2 transition-colors"
                  onClick={() => {
                    // Handle admin login
                    console.log("Admin login");
                  }}
                >
                  <ChartBarIcon className="w-5 h-5" />
                  Quản lý
                </button>
                <button
                  type="button"
                  className="flex-1 h-12 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md flex items-center justify-center gap-2 transition-colors"
                  onClick={() => {
                    // Handle sales login
                    console.log("Sales login");
                  }}
                >
                  <ShoppingCartIcon className="w-5 h-5" />
                  Bán hàng
                </button>
              </div>
            </Form>

            {/* Footer */}
            <div className="mt-8 text-center text-sm text-gray-500">
              <div className="flex items-center justify-center gap-4">
                <span className="flex items-center gap-1">
                  <Phone className="w-4 h-4" />
                  Hỗ trợ: 1900 6522
                </span>
                <span className="flex items-center gap-1">
                  <img
                    src="/lg-vn.png"
                    alt="Lá cờ Việt Nam"
                    className="w-5 h-3"
                  />
                  Tiếng Việt
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoginAdmin;
