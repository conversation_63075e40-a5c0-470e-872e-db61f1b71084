"use client";

import React, { useState } from "react";
import { EyeIcon, EyeSlashIcon, ArrowRightOnRectangleIcon } from "@heroicons/react/24/outline";
import { useColor } from "@/context/ColorContext";
import { useAuth } from "@/context/AuthContext";
import { Form, Input, message, Skeleton } from "antd";
import { Lock, Mail, Phone } from "lucide-react";
import { useRouter } from "next/navigation";
import ForgotPassword from "@/components/modules/ForgotPassword";

const LoginAdmin = () => {
  const { theme } = useColor();
  const { login } = useAuth();
  const router = useRouter();

  const [account, setAccount] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [remember, setRemember] = useState(true);
  const [isForgotOpen, setIsForgotOpen] = useState(false);

  const [form] = Form.useForm();
  const [loading] = useState(false);

  const onSubmit = () => {
    console.log("Form submitted");
    // Handle login logic here
  };

  // If forgot password is open, show ForgotPassword component
  if (isForgotOpen) {
    return <ForgotPassword onBack={() => setIsForgotOpen(false)} />;
  }

  return (
    <div className="w-full h-screen relative flex items-center justify-center">
      {/* Background image using img tag */}
      <img
        src="/login-bg.jpg"
        alt="login background"
        className="absolute inset-0 w-full h-full object-cover"
        style={{ zIndex: -1 }}
      />

      {/* Overlay */}
      <div className="absolute inset-0 bg-opacity-20" style={{ zIndex: 0 }}></div>

      {/* Login Card */}
      <div className="relative z-10 w-full max-w-md mx-4">
        {loading ? (
          <div className="flex h-screen items-center justify-center">
            <Skeleton active round avatar title />
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-lg p-8">
            {/* Logo and Brand */}
            <div className="flex flex-col items-center mb-8">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                </div>
                <span className="text-2xl font-bold text-gray-800">MiniMart</span>
              </div>
            </div>

            {/* Form */}
            <Form
              layout="vertical"
              className="space-y-4"
              onFinish={onSubmit}
              form={form}
            >
              {/* Username Input */}
              <Form.Item
                name="account"
                rules={[
                  { required: true, message: "Vui lòng nhập tên đăng nhập" },
                ]}
              >
                <Input
                  placeholder="Tên đăng nhập"
                  value={account}
                  onChange={(e) => setAccount(e.target.value)}
                  className="h-12 px-4 border border-gray-300 rounded-md"
                />
              </Form.Item>

              {/* Password Input */}
              <Form.Item
                name="password"
                rules={[{ required: true, message: "Vui lòng nhập mật khẩu" }]}
              >
                <div className="relative">
                  <Input.Password
                    placeholder="Mật khẩu"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="h-12 px-4 border border-gray-300 rounded-md"
                    iconRender={(visible) =>
                      visible ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />
                    }
                  />
                </div>
              </Form.Item>

              {/* Remember me and Forgot password */}
              <div className="flex items-center justify-between text-sm mb-6">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={remember}
                    onChange={(e) => setRemember(e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-gray-600">Duy trì đăng nhập</span>
                </label>
                <a
                  href="#"
                  className="text-blue-600 hover:underline"
                  onClick={(e) => {
                    e.preventDefault();
                    setIsForgotOpen(true);
                  }}
                >
                  Quên mật khẩu?
                </a>
              </div>

              {/* Login Button */}
              <button
                type="submit"
                className={`w-full h-12 ${theme.primary} hover:bg-blue-900 ${theme.text} font-medium rounded-md flex items-center justify-center gap-2 transition-colors`}
                onClick={() => {
                  // Handle login
                  console.log("Login");
                }}
              >
                <ArrowRightOnRectangleIcon className="w-5 h-5" />
                Đăng nhập
              </button>
            </Form>

            {/* Footer */}
            <div className="mt-8 text-center text-sm text-gray-500">
              <div className="flex items-center justify-center gap-4">
                <span className="flex items-center gap-1">
                  <Phone className="w-4 h-4" />
                  Hỗ trợ: 1900 6522
                </span>
                <span className="flex items-center gap-1">
                  <img
                    src="/lg-vn.png"
                    alt="Lá cờ Việt Nam"
                    className="w-5 h-3"
                  />
                  Tiếng Việt
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoginAdmin;
